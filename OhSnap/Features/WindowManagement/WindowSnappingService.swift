import AppKit
import Foundation

/// Service that provides window snapping functionality using the clean WindowSnapper implementation
class WindowSnappingService: @unchecked Sendable {

    // MARK: - Properties

    private let logger = LoggingService.shared
    private let serviceName = "WindowSnappingService"
    private let windowSnapper = WindowSnapper()

    // MARK: - Initialization

    init() {
        logger.info("WindowSnappingService initialized", service: serviceName)

        // Check accessibility permissions on initialization
        if !WindowSnapper.checkAccessibility() {
            logger.warning("Accessibility permissions not granted", service: serviceName)
        }
    }

    // MARK: - Public Interface

    /// Snap the frontmost window to the specified position
    /// - Parameter position: The snap position to move the window to
    @MainActor func snapFrontmostWindow(to position: SnapPosition) {
        logger.info("Snap request: \(position.description)", service: serviceName)

        // Check accessibility permissions
        guard WindowSnapper.checkAccessibility() else {
            logger.error(
                "Accessibility permissions required for window snapping", service: serviceName)
            WindowSnapper.promptForAccessibility()
            return
        }

        // Perform the snap
        windowSnapper.snapWindow(to: position)
    }

    /// Move the frontmost window to the next screen
    @MainActor func moveFrontmostWindowToNextScreen() {
        logger.info("Move to next screen request", service: serviceName)

        // Check accessibility permissions
        guard WindowSnapper.checkAccessibility() else {
            logger.error(
                "Accessibility permissions required for window movement", service: serviceName)
            WindowSnapper.promptForAccessibility()
            return
        }

        // Perform the move
        windowSnapper.moveToNextScreen()
    }

    // MARK: - Convenience Methods

    @MainActor func snapLeft() { snapFrontmostWindow(to: SnapPosition.leftHalf) }
    @MainActor func snapRight() { snapFrontmostWindow(to: SnapPosition.rightHalf) }
    @MainActor func snapTop() { snapFrontmostWindow(to: SnapPosition.topHalf) }
    @MainActor func snapBottom() { snapFrontmostWindow(to: SnapPosition.bottomHalf) }
    @MainActor func maximize() { snapFrontmostWindow(to: SnapPosition.fullscreen) }

    @MainActor func snapTopLeft() { snapFrontmostWindow(to: SnapPosition.topLeftQuarter) }
    @MainActor func snapTopRight() { snapFrontmostWindow(to: SnapPosition.topRightQuarter) }
    @MainActor func snapBottomLeft() { snapFrontmostWindow(to: SnapPosition.bottomLeftQuarter) }
    @MainActor func snapBottomRight() { snapFrontmostWindow(to: SnapPosition.bottomRightQuarter) }

    @MainActor func snapLeftThird() { snapFrontmostWindow(to: SnapPosition.leftThird) }
    @MainActor func snapCenterThird() { snapFrontmostWindow(to: SnapPosition.centerThird) }
    @MainActor func snapRightThird() { snapFrontmostWindow(to: SnapPosition.rightThird) }

    @MainActor func snapLeftTwoThirds() { snapFrontmostWindow(to: SnapPosition.leftTwoThirds) }
    @MainActor func snapCenterTwoThirds() { snapFrontmostWindow(to: SnapPosition.centerTwoThirds) }
    @MainActor func snapRightTwoThirds() { snapFrontmostWindow(to: SnapPosition.rightTwoThirds) }

    // MARK: - Legacy Compatibility

    /// Convert WindowDirection to SnapPosition for compatibility with existing code
    /// - Parameter direction: The WindowDirection to convert
    /// - Returns: The equivalent SnapPosition, or nil if no direct mapping exists
    private func convertDirectionToSnapPosition(_ direction: WindowDirection) -> SnapPosition? {
        switch direction {
        case .leftHalf: return .leftHalf
        case .rightHalf: return .rightHalf
        case .topHalf: return .topHalf
        case .bottomHalf: return .bottomHalf
        case .maximize: return .fullscreen
        case .topLeftQuarter: return .topLeftQuarter
        case .topRightQuarter: return .topRightQuarter
        case .bottomLeftQuarter: return .bottomLeftQuarter
        case .bottomRightQuarter: return .bottomRightQuarter
        case .leftThird: return .leftThird
        case .centerThird: return .centerThird
        case .rightThird: return .rightThird
        case .leftTwoThirds: return .leftTwoThirds
        case .centerTwoThirds: return .centerTwoThirds
        case .rightTwoThirds: return .rightTwoThirds
        case .custom(let rect): return .custom(rect)
        default:
            logger.warning(
                "No SnapPosition mapping for WindowDirection: \(direction)", service: serviceName)
            return nil
        }
    }

    /// Snap window using WindowDirection for backward compatibility
    /// - Parameter direction: The WindowDirection to snap to
    @MainActor func snapFrontmostWindow(to direction: WindowDirection) {
        guard let position = convertDirectionToSnapPosition(direction) else {
            logger.error(
                "Cannot convert WindowDirection \(direction) to SnapPosition", service: serviceName)
            return
        }

        snapFrontmostWindow(to: position)
    }

    // MARK: - Debug Helpers

    func printDisplayInfo() {
        logger.info("=== Display Configuration ===", service: serviceName)
        for (i, screen) in NSScreen.screens.enumerated() {
            logger.info("Screen \(i):", service: serviceName)
            logger.info("  Frame: \(screen.frame)", service: serviceName)
            logger.info("  Visible: \(screen.visibleFrame)", service: serviceName)
            logger.info("  Main: \(screen == NSScreen.main)", service: serviceName)
        }
    }

    /// Check if accessibility is enabled and prompt if needed
    func ensureAccessibilityPermissions() -> Bool {
        if WindowSnapper.checkAccessibility() {
            return true
        } else {
            logger.warning("Prompting for accessibility permissions", service: serviceName)
            WindowSnapper.promptForAccessibility()
            return false
        }
    }
}
