import ApplicationServices
import Cocoa

// Use the SnapPosition from SharedTypes.swift
typealias WindowSnapPosition = SnapPosition

/// Clean window snapping implementation using direct Accessibility APIs
class WindowSnapper {

    // MARK: - Properties

    private let logger = LoggingService.shared
    private let serviceName = "WindowSnapper"

    // MARK: - Core Window Operations

    private func getFrontmostWindow() -> AXUIElement? {
        guard let app = NSWorkspace.shared.frontmostApplication else {
            logger.warning("No frontmost application found", service: serviceName)
            return nil
        }

        let appElement = AXUIElementCreateApplication(app.processIdentifier)

        var window: CFTypeRef?
        guard
            AXUIElementCopyAttributeValue(
                appElement, kAXFocusedWindowAttribute as CFString, &window) == .success
        else {
            logger.warning(
                "Failed to get focused window for app: \(app.localizedName ?? "Unknown")",
                service: serviceName)
            return nil
        }

        logger.debug(
            "Found frontmost window for app: \(app.localizedName ?? "Unknown")",
            service: serviceName)
        return (window as! AXUIElement)
    }

    private func getWindowFrame(_ window: AXUIElement) -> CGRect? {
        var position: CFTypeRef?
        var size: CFTypeRef?

        guard
            AXUIElementCopyAttributeValue(window, kAXPositionAttribute as CFString, &position)
                == .success,
            AXUIElementCopyAttributeValue(window, kAXSizeAttribute as CFString, &size) == .success
        else {
            logger.error("Failed to get window frame attributes", service: serviceName)
            return nil
        }

        let posValue = position as! AXValue
        let sizeValue = size as! AXValue

        var point = CGPoint.zero
        var cgSize = CGSize.zero

        AXValueGetValue(posValue, .cgPoint, &point)
        AXValueGetValue(sizeValue, .cgSize, &cgSize)

        let frame = CGRect(origin: point, size: cgSize)
        logger.debug("Window frame: \(frame)", service: serviceName)
        return frame
    }

    private func setWindowFrame(_ window: AXUIElement, to rect: CGRect) {
        var origin = rect.origin
        var size = rect.size

        let posValue = AXValueCreate(.cgPoint, &origin)!
        let sizeValue = AXValueCreate(.cgSize, &size)!

        let posResult = AXUIElementSetAttributeValue(
            window, kAXPositionAttribute as CFString, posValue)
        let sizeResult = AXUIElementSetAttributeValue(
            window, kAXSizeAttribute as CFString, sizeValue)

        if posResult == .success && sizeResult == .success {
            logger.info("Successfully positioned window to \(rect)", service: serviceName)
        } else {
            logger.error(
                "Failed to set window frame - Position: \(posResult), Size: \(sizeResult)",
                service: serviceName)
        }
    }

    // MARK: - Screen Utilities

    private func getScreenForWindow(_ window: AXUIElement) -> NSScreen? {
        guard let frame = getWindowFrame(window) else {
            logger.warning("Cannot determine screen - no window frame", service: serviceName)
            return NSScreen.main
        }

        let windowCenter = CGPoint(x: frame.midX, y: frame.midY)

        // Find screen containing window center
        let screen =
            NSScreen.screens.first { screen in
                screen.frame.contains(windowCenter)
            } ?? NSScreen.main

        if let screenNumber = screen?.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
            as? NSNumber
        {
            logger.debug("Window is on screen \(screenNumber)", service: serviceName)
        }

        return screen
    }

    // MARK: - Snap Positions

    func snapWindow(to position: WindowSnapPosition) {
        logger.info("┌─ Starting window snap to \(position.description) ─┐", service: serviceName)

        guard let window = getFrontmostWindow() else {
            logger.error("│ No frontmost window found", service: serviceName)
            logger.info("└─ Window snap failed ─┘", service: serviceName)
            return
        }

        guard let screen = getScreenForWindow(window) else {
            logger.error("│ No target screen found", service: serviceName)
            logger.info("└─ Window snap failed ─┘", service: serviceName)
            return
        }

        // Use visibleFrame - this automatically excludes dock and menu bar
        let workArea = screen.visibleFrame
        logger.debug("│ Work area: \(workArea)", service: serviceName)

        let targetFrame = calculateFrame(for: position, in: workArea)
        logger.debug("│ Target frame: \(targetFrame)", service: serviceName)

        setWindowFrame(window, to: targetFrame)

        logger.info("└─ Window snap completed ─┘", service: serviceName)
    }

    private func calculateFrame(for position: WindowSnapPosition, in workArea: CGRect) -> CGRect {
        let x = workArea.minX
        let y = workArea.minY
        let w = workArea.width
        let h = workArea.height
        let halfW = w / 2
        let halfH = h / 2
        let thirdW = w / 3
        let _ = h / 3  // thirdH not used in current implementation

        switch position {
        // Halves
        case .leftHalf: return CGRect(x: x, y: y, width: halfW, height: h)
        case .rightHalf: return CGRect(x: x + halfW, y: y, width: halfW, height: h)
        case .topHalf: return CGRect(x: x, y: y, width: w, height: halfH)
        case .bottomHalf: return CGRect(x: x, y: y + halfH, width: w, height: halfH)

        // Quarters
        case .topLeftQuarter: return CGRect(x: x, y: y, width: halfW, height: halfH)
        case .topRightQuarter: return CGRect(x: x + halfW, y: y, width: halfW, height: halfH)
        case .bottomLeftQuarter: return CGRect(x: x, y: y + halfH, width: halfW, height: halfH)
        case .bottomRightQuarter:
            return CGRect(x: x + halfW, y: y + halfH, width: halfW, height: halfH)

        // Thirds
        case .leftThird: return CGRect(x: x, y: y, width: thirdW, height: h)
        case .centerThird: return CGRect(x: x + thirdW, y: y, width: thirdW, height: h)
        case .rightThird: return CGRect(x: x + (thirdW * 2), y: y, width: thirdW, height: h)

        // Two thirds
        case .leftTwoThirds: return CGRect(x: x, y: y, width: thirdW * 2, height: h)
        case .centerTwoThirds: return CGRect(x: x + thirdW / 2, y: y, width: thirdW * 2, height: h)
        case .rightTwoThirds: return CGRect(x: x + thirdW, y: y, width: thirdW * 2, height: h)

        // Special positions
        case .fullscreen: return workArea
        case .custom(let rect): return rect
        }
    }

    // MARK: - Multi-Display Support

    func moveToNextScreen() {
        logger.info("┌─ Moving window to next screen ─┐", service: serviceName)

        guard let window = getFrontmostWindow(),
            let currentFrame = getWindowFrame(window),
            NSScreen.screens.count > 1
        else {
            logger.warning(
                "│ Cannot move to next screen - insufficient screens or no window",
                service: serviceName)
            logger.info("└─ Move to next screen failed ─┘", service: serviceName)
            return
        }

        let currentScreen = getScreenForWindow(window)!
        let currentIndex = NSScreen.screens.firstIndex(of: currentScreen) ?? 0
        let nextScreen = NSScreen.screens[(currentIndex + 1) % NSScreen.screens.count]

        logger.debug(
            "│ Moving from screen \(currentIndex) to screen \((currentIndex + 1) % NSScreen.screens.count)",
            service: serviceName)

        // Use workspace coordinate system approach for relative positioning
        let currentWork = currentScreen.visibleFrame
        let nextWork = nextScreen.visibleFrame

        let relativeX = (currentFrame.origin.x - currentWork.origin.x) / currentWork.width
        let relativeY = (currentFrame.origin.y - currentWork.origin.y) / currentWork.height
        let relativeW = currentFrame.width / currentWork.width
        let relativeH = currentFrame.height / currentWork.height

        let newFrame = CGRect(
            x: nextWork.origin.x + (relativeX * nextWork.width),
            y: nextWork.origin.y + (relativeY * nextWork.height),
            width: min(relativeW * nextWork.width, nextWork.width * 0.9),
            height: min(relativeH * nextWork.height, nextWork.height * 0.9)
        )

        logger.debug("│ New frame: \(newFrame)", service: serviceName)
        setWindowFrame(window, to: newFrame)

        logger.info("└─ Move to next screen completed ─┘", service: serviceName)
    }

    // MARK: - Accessibility Check

    static func checkAccessibility() -> Bool {
        return AXIsProcessTrusted()
    }

    static func promptForAccessibility() {
        if !checkAccessibility() {
            let logger = LoggingService.shared
            logger.warning(
                "Accessibility access required for window snapping", service: "WindowSnapper")

            // This will show the accessibility prompt
            let options = [kAXTrustedCheckOptionPrompt.takeRetainedValue(): true]
            AXIsProcessTrustedWithOptions(options as CFDictionary)
        }
    }
}
